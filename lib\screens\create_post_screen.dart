import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wicker/services/post_service.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class CreatePostScreen extends StatefulWidget {
  const CreatePostScreen({super.key});

  @override
  _CreatePostScreenState createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen> {
  final _textController = TextEditingController();
  final PostService _postService = PostService();
  final ImagePicker _picker = ImagePicker();

  List<XFile> _mediaFiles = [];
  bool _isLoading = false;

  Future<void> _pickMedia() async {
    final List<XFile> pickedFiles = await _picker.pickMultipleMedia();
    setState(() {
      _mediaFiles = pickedFiles;
    });
  }

  void _submitPost() async {
    if (_textController.text.isEmpty && _mediaFiles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add some content or media to your post'),
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final message = await _postService.createPost(
        textContent: _textController.text,
        mediaFiles: _mediaFiles,
      );
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(message)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString().replaceFirst('Exception: ', ''))),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create a New Post'),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: NeuTextButton(
              onPressed: _isLoading ? null : _submitPost,
              buttonColor: Colors.teal,
              buttonHeight: 60,
              enableAnimation: true,
              text: Text(
                _isLoading ? 'Posting...' : 'Post',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: NeuContainer(
                color: Colors.white,
                child: TextField(
                  controller: _textController,
                  maxLines: null, // Allows for unlimited lines
                  expands: true,
                  decoration: const InputDecoration(
                    hintText: "What's on your mind?",
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.all(12),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildMediaPreviews(),
            const SizedBox(height: 16),
            NeuIconButton(
              enableAnimation: true,
              icon: const Icon(EvaIcons.imageOutline),
              onPressed: _pickMedia,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaPreviews() {
    if (_mediaFiles.isEmpty) return const SizedBox.shrink();
    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _mediaFiles.length,
        itemBuilder: (context, index) {
          final imageFile = _mediaFiles[index];
          return Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              // Use Image.network for web and Image.file for mobile
              child: kIsWeb
                  ? Image.network(
                      imageFile.path,
                      width: 100,
                      height: 100,
                      fit: BoxFit.cover,
                    )
                  : Image.file(
                      File(imageFile.path),
                      width: 100,
                      height: 100,
                      fit: BoxFit.cover,
                    ),
            ),
          );
        },
      ),
    );
  }
}
