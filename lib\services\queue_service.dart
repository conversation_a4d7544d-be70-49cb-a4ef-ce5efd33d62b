import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:wicker/services/places_service.dart'; // We'll use the WickerHttpClient
import 'package:wicker/services/config_service.dart';

class QueueService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  Future<String> createQueue({
    required String name,
    required bool isPrivate,
  }) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.post(
        Uri.parse('$baseUrl/api/queues/create'),
        body: jsonEncode({'name': name, 'is_private': isPrivate}),
      );

      final responseBody = jsonDecode(response.body);
      if (response.statusCode == 201) {
        return responseBody['msg'];
      } else {
        throw Exception(responseBody['msg'] ?? 'Failed to create queue');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Method to fetch the user's queues
  Future<List<Map<String, dynamic>>> getMyQueues() async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.get(Uri.parse('$baseUrl/api/queues/'));
      if (response.statusCode == 200) {
        List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception('Failed to load queues');
      }
    } catch (e) {
      rethrow;
    }
  }
}
