import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wicker/widgets/media_preview_tile.dart';

// Mock XFile for testing
class MockXFile extends XFile {
  final String _path;
  final String? _mimeType;
  final Uint8List? _bytes;

  MockXFile(this._path, {String? mimeType, Uint8List? bytes})
      : _mimeType = mimeType,
        _bytes = bytes,
        super(_path);

  @override
  String get path => _path;

  @override
  String? get mimeType => _mimeType;

  @override
  Future<Uint8List> readAsBytes() async {
    if (_bytes != null) {
      return _bytes!;
    }
    throw Exception('Mock file read error');
  }
}

void main() {
  group('MediaPreviewTile', () {
    testWidgets('shows loading indicator initially', (WidgetTester tester) async {
      // Create a mock image file
      final mockFile = MockXFile(
        'test_image.jpg',
        mimeType: 'image/jpeg',
        bytes: Uint8List.fromList([1, 2, 3, 4]), // Mock image bytes
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MediaPreviewTile(
              mediaFile: mockFile,
              onRemove: () {},
            ),
          ),
        ),
      );

      // Initially should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('shows error state when file reading fails', (WidgetTester tester) async {
      // Create a mock file that will fail to read
      final mockFile = MockXFile(
        'test_image.jpg',
        mimeType: 'image/jpeg',
        // No bytes provided, will cause readAsBytes to throw
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MediaPreviewTile(
              mediaFile: mockFile,
              onRemove: () {},
            ),
          ),
        ),
      );

      // Wait for the async operation to complete
      await tester.pumpAndSettle();

      // Should show error state
      expect(find.byIcon(Icons.broken_image), findsOneWidget);
      expect(find.text('Error'), findsOneWidget);
    });

    testWidgets('shows image when successfully loaded', (WidgetTester tester) async {
      // Create a simple 1x1 pixel PNG image bytes
      final pngBytes = Uint8List.fromList([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 dimensions
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, // bit depth, color type, etc.
        0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, // IDAT chunk
        0x54, 0x08, 0x99, 0x01, 0x01, 0x01, 0x00, 0x00, // image data
        0xFE, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // more data
        0xE2, 0x21, 0xBC, 0x33, 0x00, 0x00, 0x00, 0x00, // IEND chunk
        0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ]);

      final mockFile = MockXFile(
        'test_image.png',
        mimeType: 'image/png',
        bytes: pngBytes,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MediaPreviewTile(
              mediaFile: mockFile,
              onRemove: () {},
            ),
          ),
        ),
      );

      // Wait for the async operation to complete
      await tester.pumpAndSettle();

      // Should show the image
      expect(find.byType(Image), findsOneWidget);
    });

    testWidgets('identifies video files correctly', (WidgetTester tester) async {
      final mockFile = MockXFile(
        'test_video.mp4',
        mimeType: 'video/mp4',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MediaPreviewTile(
              mediaFile: mockFile,
              onRemove: () {},
            ),
          ),
        ),
      );

      // Wait for the async operation to complete
      await tester.pumpAndSettle();

      // For video files, it should show loading or error state
      // (since we can't easily mock video controller in tests)
      final hasLoadingIndicator = find.byType(CircularProgressIndicator).evaluate().isNotEmpty;
      final hasErrorIcon = find.byIcon(Icons.broken_image).evaluate().isNotEmpty;
      expect(hasLoadingIndicator || hasErrorIcon, isTrue);
    });

    testWidgets('calls onRemove when close button is tapped', (WidgetTester tester) async {
      bool removeCalled = false;
      final mockFile = MockXFile(
        'test_image.jpg',
        mimeType: 'image/jpeg',
        bytes: Uint8List.fromList([1, 2, 3, 4]),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MediaPreviewTile(
              mediaFile: mockFile,
              onRemove: () {
                removeCalled = true;
              },
            ),
          ),
        ),
      );

      // Find and tap the close button
      await tester.tap(find.byIcon(Icons.close));
      await tester.pump();

      expect(removeCalled, isTrue);
    });
  });
}
