import 'dart:io';
import 'dart:typed_data';
import 'dart:html' as html;
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:video_player/video_player.dart';

class MediaPreviewTile extends StatefulWidget {
  final XFile mediaFile;
  final VoidCallback onRemove;

  const MediaPreviewTile({
    super.key,
    required this.mediaFile,
    required this.onRemove,
  });

  @override
  State<MediaPreviewTile> createState() => _MediaPreviewTileState();
}

class _MediaPreviewTileState extends State<MediaPreviewTile> {
  VideoPlayerController? _videoController;
  bool _isVideo = false;
  Uint8List? _fileBytes;
  String? _dataUrl;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  String? _fileTypeInfo;

  // Enhanced video file detection
  bool _isVideoFile(XFile file) {
    _fileTypeInfo = 'MIME: ${file.mimeType}, Path: ${file.path}';

    // Priority 1: Check MIME type if available and reliable
    if (file.mimeType != null && file.mimeType!.startsWith('video/')) {
      return true;
    }

    // Priority 2: For blob URLs on web, be more conservative
    if (kIsWeb && file.path.startsWith('blob:')) {
      // Only trust MIME type for blob URLs since path analysis is unreliable
      return file.mimeType != null && file.mimeType!.startsWith('video/');
    }

    // Priority 3: Check file extension as fallback
    try {
      String pathToCheck = file.path;
      
      if (file.path.contains('/')) {
        try {
          final uri = Uri.parse(file.path);
          pathToCheck = uri.pathSegments.isNotEmpty ? uri.pathSegments.last : file.path;
        } catch (e) {
          pathToCheck = file.path.split('/').last;
        }
      }

      if (pathToCheck.contains('.')) {
        final extension = pathToCheck.split('.').last.toLowerCase();
        final videoExtensions = {
          'mp4', 'mov', 'avi', 'mkv', 'webm', 'ogv', 
          'm4v', 'wmv', 'flv', '3gp', 'ogg', 'quicktime'
        };
        return videoExtensions.contains(extension);
      }
    } catch (e) {
      debugPrint('Error extracting file extension: $e');
    }

    return false;
  }

  // Advanced image data validation
  bool _isValidImageData(Uint8List bytes) {
    if (bytes.isEmpty || bytes.length < 10) return false;
    
    // Check for common image file signatures
    // JPEG: FF D8 FF
    if (bytes.length >= 3 && 
        bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
      return true;
    }
    
    // PNG: 89 50 4E 47 0D 0A 1A 0A
    if (bytes.length >= 8 && 
        bytes[0] == 0x89 && bytes[1] == 0x50 && 
        bytes[2] == 0x4E && bytes[3] == 0x47 &&
        bytes[4] == 0x0D && bytes[5] == 0x0A &&
        bytes[6] == 0x1A && bytes[7] == 0x0A) {
      return true;
    }
    
    // GIF: 47 49 46 38 (GIF8)
    if (bytes.length >= 6 && 
        bytes[0] == 0x47 && bytes[1] == 0x49 && 
        bytes[2] == 0x46 && bytes[3] == 0x38) {
      return true;
    }
    
    // WebP: 52 49 46 46 (RIFF) ... 57 45 42 50 (WEBP)
    if (bytes.length >= 12 && 
        bytes[0] == 0x52 && bytes[1] == 0x49 && 
        bytes[2] == 0x46 && bytes[3] == 0x46 &&
        bytes[8] == 0x57 && bytes[9] == 0x45 && 
        bytes[10] == 0x42 && bytes[11] == 0x50) {
      return true;
    }

    // BMP: 42 4D
    if (bytes.length >= 2 && 
        bytes[0] == 0x42 && bytes[1] == 0x4D) {
      return true;
    }
    
    return false;
  }

  // Check if bytes represent video data
  bool _looksLikeVideoData(Uint8List bytes) {
    if (bytes.isEmpty || bytes.length < 12) return false;
    
    // MP4: starts with ftyp box
    if (bytes.length >= 8 && bytes[4] == 0x66 && bytes[5] == 0x74 && 
        bytes[6] == 0x79 && bytes[7] == 0x70) {
      return true;
    }
    
    // WebM: EBML header
    if (bytes.length >= 4 && bytes[0] == 0x1A && bytes[1] == 0x45 && 
        bytes[2] == 0xDF && bytes[3] == 0xA3) {
      return true;
    }
    
    // AVI: RIFF...AVI
    if (bytes.length >= 12 && 
        bytes[0] == 0x52 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x46 &&
        bytes[8] == 0x41 && bytes[9] == 0x56 && bytes[10] == 0x49 && bytes[11] == 0x20) {
      return true;
    }
    
    return false;
  }

  // Create data URL for images to bypass blob URL issues
  String _createDataUrl(Uint8List bytes, String mimeType) {
    final base64String = base64Encode(bytes);
    return 'data:$mimeType;base64,$base64String';
  }

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      debugPrint('Initializing media file: ${widget.mediaFile.path}');
      debugPrint('MIME type: ${widget.mediaFile.mimeType}');

      // Read bytes first to analyze the actual file content
      final bytes = await widget.mediaFile.readAsBytes();
      debugPrint('Read ${bytes.length} bytes');

      // Determine file type based on both metadata and content analysis
      bool isVideoByContent = _looksLikeVideoData(bytes);
      bool isVideoByMetadata = _isVideoFile(widget.mediaFile);
      bool isImageByContent = _isValidImageData(bytes);

      debugPrint('Analysis: videoByContent=$isVideoByContent, videoByMetadata=$isVideoByMetadata, imageByContent=$isImageByContent');

      // Decision logic: prioritize content analysis over metadata
      bool shouldTreatAsVideo = isVideoByContent || (isVideoByMetadata && !isImageByContent);

      setState(() {
        _isVideo = shouldTreatAsVideo;
        _isLoading = true;
        _hasError = false;
        _errorMessage = null;
        _fileBytes = bytes;
      });

      if (_isVideo) {
        await _initializeVideo();
      } else {
        await _initializeImage();
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error in initialization: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }

  Future<void> _initializeVideo() async {
    try {
      debugPrint('Initializing video');
      
      if (kIsWeb) {
        // For web, prefer the original blob URL if available
        if (widget.mediaFile.path.startsWith('blob:')) {
          _videoController = VideoPlayerController.networkUrl(
            Uri.parse(widget.mediaFile.path)
          );
        } else {
          // Fallback: create blob from bytes
          final blob = html.Blob([_fileBytes!], widget.mediaFile.mimeType ?? 'video/mp4');
          final url = html.Url.createObjectUrlFromBlob(blob);
          _videoController = VideoPlayerController.networkUrl(Uri.parse(url));
        }
        
        await _videoController!.initialize();
      } else {
        _videoController = VideoPlayerController.file(
          File(widget.mediaFile.path)
        );
        await _videoController!.initialize();
      }
    } catch (e) {
      debugPrint('Error initializing video controller: $e');
      setState(() {
        _hasError = true;
        _errorMessage = 'Video initialization failed: $e';
      });
    }
  }

  Future<void> _initializeImage() async {
    try {
      debugPrint('Initializing image');
      
      if (!_isValidImageData(_fileBytes!)) {
        throw Exception('Invalid image data detected');
      }

      if (kIsWeb) {
        // CRITICAL FIX: Use data URL instead of blob URL to avoid the track metadata error
        String mimeType = widget.mediaFile.mimeType ?? 'image/jpeg';
        
        // Ensure we have a valid image MIME type
        if (!mimeType.startsWith('image/')) {
          // Try to detect from file signature
          if (_fileBytes![0] == 0xFF && _fileBytes![1] == 0xD8) {
            mimeType = 'image/jpeg';
          } else if (_fileBytes![0] == 0x89 && _fileBytes![1] == 0x50) {
            mimeType = 'image/png';
          } else if (_fileBytes![0] == 0x47 && _fileBytes![1] == 0x49) {
            mimeType = 'image/gif';
          } else if (_fileBytes![0] == 0x52 && _fileBytes![1] == 0x49) {
            mimeType = 'image/webp';
          } else {
            mimeType = 'image/jpeg'; // Default fallback
          }
        }
        
        _dataUrl = _createDataUrl(_fileBytes!, mimeType);
        debugPrint('Created data URL with MIME type: $mimeType');
      }
      
      debugPrint('Image initialization successful');
    } catch (e) {
      debugPrint('Error initializing image: $e');
      setState(() {
        _hasError = true;
        _errorMessage = 'Image initialization failed: $e';
      });
    }
  }

  @override
  void dispose() {
    _videoController?.dispose();
    // Clean up blob URLs if created
    if (kIsWeb && _dataUrl != null && _dataUrl!.startsWith('blob:')) {
      html.Url.revokeObjectUrl(_dataUrl!);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            child: _buildPreviewWidget(),
          ),
          Positioned(
            top: -8,
            right: -8,
            child: GestureDetector(
              onTap: widget.onRemove,
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 18
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewWidget() {
    const double containerSize = 100;

    if (_hasError) {
      return Container(
        width: containerSize,
        height: containerSize,
        decoration: BoxDecoration(
          color: Colors.grey.shade300,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 24),
            const SizedBox(height: 4),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Text(
                'Load Error',
                style: const TextStyle(fontSize: 10, color: Colors.red),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      );
    }

    if (_isLoading) {
      return Container(
        width: containerSize,
        height: containerSize,
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      );
    }

    if (_isVideo) {
      return _buildVideoPreview(containerSize);
    } else {
      return _buildImagePreview(containerSize);
    }
  }

  Widget _buildVideoPreview(double size) {
    if (_videoController?.value.isInitialized ?? false) {
      return SizedBox(
        width: size,
        height: size,
        child: Stack(
          alignment: Alignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: VideoPlayer(_videoController!),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: Colors.black26,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(
                Icons.play_circle_fill, 
                color: Colors.white, 
                size: 32
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Icon(Icons.videocam, color: Colors.white, size: 32),
        ),
      );
    }
  }

  Widget _buildImagePreview(double size) {
    if (kIsWeb && _dataUrl != null) {
      // Use data URL for web to avoid blob URL issues
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.network(
          _dataUrl!,
          width: size,
          height: size,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('Image.network (data URL) error: $error');
            return _buildImageFallback(size);
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            );
          },
        ),
      );
    } else if (_fileBytes != null && _fileBytes!.isNotEmpty) {
      // Fallback to memory image
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.memory(
          _fileBytes!,
          width: size,
          height: size,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('Image.memory error: $error');
            return _buildImageFallback(size);
          },
        ),
      );
    } else if (!kIsWeb) {
      // File image for non-web platforms
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.file(
          File(widget.mediaFile.path),
          width: size,
          height: size,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildImageFallback(size);
          },
        ),
      );
    } else {
      return _buildImageFallback(size);
    }
  }

  Widget _buildImageFallback(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.grey.shade300,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.broken_image, color: Colors.grey, size: 32),
          SizedBox(height: 4),
          Text(
            'Image\nError',
            style: TextStyle(fontSize: 10, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}