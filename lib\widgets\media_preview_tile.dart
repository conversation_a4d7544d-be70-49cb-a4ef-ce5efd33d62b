import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:video_player/video_player.dart';
import 'dart:typed_data';

class MediaPreviewTile extends StatefulWidget {
  final XFile mediaFile;
  final VoidCallback onRemove;

  const MediaPreviewTile({
    super.key,
    required this.mediaFile,
    required this.onRemove,
  });

  @override
  State<MediaPreviewTile> createState() => _MediaPreviewTileState();
}

class _MediaPreviewTileState extends State<MediaPreviewTile> {
  VideoPlayerController? _videoController;
  bool _isVideo = false;
  Uint8List? _fileBytes;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    // Simplified and more robust check for video
    _isVideo = _isVideoFile(widget.mediaFile);


    try {
      if (_isVideo) {
        print('video found');
        // Initialize video controller
        if (kIsWeb) {
          print('web video found');
          _videoController = VideoPlayerController.networkUrl(Uri.parse(widget.mediaFile.path));
        } else {
          print('mobile video found');
          _videoController = VideoPlayerController.file(File(widget.mediaFile.path));
        }
        await _videoController!.initialize();
      } else if (kIsWeb) {
        // For web images, read bytes
        _fileBytes = await widget.mediaFile.readAsBytes();
      }
    } catch (e) {
      print("Error initializing media preview: $e");
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  bool _isVideoFile(XFile file) {
    String mimeType = file.mimeType ?? '';
    print('mimeType: $mimeType');
    if (mimeType.startsWith('video/')) {
      return true;
    }
    // Fallback for mobile where mimeType can be null
    String path = file.path.toLowerCase();
    return path.endsWith('.mp4') || path.endsWith('.mov') || path.endsWith('.avi') || path.endsWith('.mkv');
  }

  @override
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            child: _buildPreviewWidget(),
          ),
          Positioned(
            top: -8, right: -8,
            child: GestureDetector(
              onTap: widget.onRemove,
              child: Container(
                decoration: const BoxDecoration(color: Colors.black54, shape: BoxShape.circle),
                child: const Icon(Icons.close, color: Colors.white, size: 18),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewWidget() {
    const double size = 100;
    if (_isLoading) {
      return Container(width: size, height: size, color: Colors.grey.shade200, child: const Center(child: CircularProgressIndicator()));
    }

    // THE FIX: This logic is now cleaner. If it's a video, use the video player. Period.
    if (_isVideo) {
      print('video found');
      return (_videoController?.value.isInitialized ?? false)
          ? SizedBox(width: size, height: size, child: VideoPlayer(_videoController!))
          : Container(width: size, height: size, color: Colors.black, child: const Center(child: Icon(Icons.videocam_off)));
    } else {
      print('must be an image');
      // If it's not a video, it must be an image.
      return kIsWeb
          ? Image.memory(_fileBytes!, width: size, height: size, fit: BoxFit.cover)
          : Image.file(File(widget.mediaFile.path), width: size, height: size, fit: BoxFit.cover);
    }
  }
}